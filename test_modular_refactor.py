#!/usr/bin/env python3
"""
测试模块化重构

验证重构后的代码是否正常工作，包括：
1. 核心接口和模型
2. 各个模块的功能
3. 模块化TTS引擎
4. 兼容性适配器
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from tts_demo.core.models import TTSConfig, PlaybackState, ProcessingStats
from tts_demo.core.events import SimpleEventBus, TextChunkEvent, AudioGeneratedEvent
from tts_demo.processors import OptimizedTextProcessor
from tts_demo.audio import EdgeTTSGenerator, SmartAudioCache
from tts_demo.queues import AdaptiveQueueManager
from tts_demo.engines import TTSEngineFactory, ModularStreamTTSEngine
from tts_demo.compatibility import LegacyStreamSpeaker


async def test_core_models():
    """测试核心模型"""
    print("🧪 测试核心模型...")
    
    # 测试TTSConfig
    config = TTSConfig(
        voice="zh-CN-YunjianNeural",
        first_chunk_size=5,
        debug_mode=True
    )
    print(f"  ✅ TTSConfig创建成功: {config.voice}")
    
    # 测试ProcessingStats
    stats = ProcessingStats()
    stats.total_chunks = 10
    stats.total_characters = 100
    stats_dict = stats.to_dict()
    print(f"  ✅ ProcessingStats转换成功: {len(stats_dict)} 个字段")
    
    return True


async def test_event_system():
    """测试事件系统"""
    print("\n📡 测试事件系统...")
    
    event_bus = SimpleEventBus()
    received_events = []
    
    # 订阅事件
    def event_handler(event):
        received_events.append(event.event_type)
    
    event_bus.subscribe("test_event", event_handler)
    
    # 启动事件总线
    await event_bus.start()
    
    # 发送事件
    from tts_demo.core.events import Event
    from dataclasses import dataclass
    
    @dataclass
    class TestEvent(Event):
        @property
        def event_type(self) -> str:
            return "test_event"
    
    await event_bus.emit(TestEvent())
    await asyncio.sleep(0.1)  # 等待事件处理
    
    await event_bus.stop()
    
    print(f"  ✅ 事件系统测试成功，接收到 {len(received_events)} 个事件")
    return True


async def test_text_processor():
    """测试文本处理器"""
    print("\n📝 测试文本处理器...")
    
    processor = OptimizedTextProcessor(debug_mode=False)
    
    # 测试文本过滤
    test_text = "这是一个测试```code```文本，包含[链接](url)。"
    filtered = processor.filter_content(test_text)
    print(f"  ✅ 文本过滤: '{test_text}' -> '{filtered}'")
    
    # 测试分词
    segmented_pos = processor.segment_text("人工智能技术正在快速发展", 3)
    print(f"  ✅ 分词测试: 分割位置 = {segmented_pos}")
    
    # 测试断句位置查找
    positions = processor.find_break_positions("这是测试，包含逗号。", ['，', '。'])
    print(f"  ✅ 断句位置: {positions}")
    
    # 测试缓存
    cache_info = processor.get_cache_info()
    print(f"  ✅ 缓存信息: 命中率 = {cache_info['hit_rate']:.2%}")
    
    return True


async def test_audio_cache():
    """测试音频缓存"""
    print("\n💾 测试音频缓存...")
    
    cache = SmartAudioCache(max_items=5, max_memory_mb=1.0)
    
    # 创建测试音频数据
    from tts_demo.core.models import AudioData
    import io
    
    audio_data = AudioData(
        data=io.BytesIO(b"fake audio data"),
        text="测试音频"
    )
    
    # 测试缓存操作
    key = "test_key"
    success = cache.put(key, audio_data)
    print(f"  ✅ 缓存添加: {success}")
    
    retrieved = cache.get(key)
    print(f"  ✅ 缓存获取: {retrieved is not None}")
    
    stats = cache.get_stats()
    print(f"  ✅ 缓存统计: 命中率 = {stats['hit_rate']:.2%}")
    
    return True


async def test_adaptive_queue():
    """测试自适应队列"""
    print("\n📋 测试自适应队列...")
    
    queue = AdaptiveQueueManager(initial_size=2, max_size=5, name="test_queue")
    
    # 测试基本操作
    await queue.put("item1")
    await queue.put("item2")
    print(f"  ✅ 队列添加: 当前大小 = {queue.qsize()}")
    
    # 测试扩容
    await queue.put("item3")  # 这应该触发扩容
    print(f"  ✅ 队列扩容: 最大大小 = {queue.current_max_size}")
    
    # 测试获取
    item = await queue.get()
    queue.task_done()
    print(f"  ✅ 队列获取: {item}")
    
    stats = queue.get_stats()
    print(f"  ✅ 队列统计: 扩容次数 = {stats['expansion_count']}")
    
    return True


async def test_modular_engine():
    """测试模块化引擎"""
    print("\n🚀 测试模块化引擎...")
    
    config = TTSConfig(
        voice="zh-CN-YunjianNeural",
        first_chunk_size=3,
        debug_mode=True,
        enable_cache=True,
        cache_size=5
    )
    
    # 创建引擎
    engine = TTSEngineFactory.create_stream_engine(config)
    print(f"  ✅ 引擎创建成功: {type(engine).__name__}")
    
    # 测试启动和停止
    await engine.start()
    print(f"  ✅ 引擎启动: 状态 = {engine.get_state()}")
    
    # 模拟简单的流处理
    async def mock_stream():
        texts = ["人工", "智能", "技术", "正在", "发展"]
        for text in texts:
            yield {"content": text}
            await asyncio.sleep(0.1)
    
    def content_extractor(chunk):
        return chunk.get("content")
    
    # 处理流（短时间）
    try:
        await asyncio.wait_for(
            engine.process_stream(mock_stream(), content_extractor),
            timeout=3.0
        )
    except asyncio.TimeoutError:
        print("  ⏰ 流处理超时（预期行为）")
    
    await engine.stop()
    print(f"  ✅ 引擎停止: 状态 = {engine.get_state()}")
    
    # 获取统计信息
    stats = engine.get_stats()
    print(f"  ✅ 统计信息: 处理块数 = {stats.total_chunks}")
    
    return True


async def test_legacy_compatibility():
    """测试兼容性适配器"""
    print("\n🔄 测试兼容性适配器...")
    
    # 创建兼容适配器
    speaker = LegacyStreamSpeaker(
        voice="zh-CN-YunjianNeural",
        first_chunk_size=3,
        debug_mode=True,
        on_text_chunk=lambda text: print(f"    📢 朗读: {text[:20]}...")
    )
    print(f"  ✅ 兼容适配器创建成功")
    
    # 测试属性访问
    print(f"  ✅ 配置访问: first_chunk_size = {speaker.first_chunk_size}")
    print(f"  ✅ 状态访问: state = {speaker.state}")
    print(f"  ✅ 运行状态: is_running = {speaker.is_running}")
    
    # 测试统计信息
    stats = speaker.get_stats()
    print(f"  ✅ 统计信息: {len(stats)} 个字段")
    
    detailed_stats = speaker.get_detailed_stats()
    print(f"  ✅ 详细统计: {len(detailed_stats)} 个组件")
    
    return True


async def test_engine_factory():
    """测试引擎工厂"""
    print("\n🏭 测试引擎工厂...")
    
    config = TTSConfig(debug_mode=True)
    
    # 测试不同的工厂方法
    engine1 = TTSEngineFactory.create_stream_engine(config)
    print(f"  ✅ 标准引擎创建: {type(engine1).__name__}")
    
    engine2 = TTSEngineFactory.create_without_cache(config)
    print(f"  ✅ 无缓存引擎创建: 缓存启用 = {engine2.cache_manager is not None}")
    
    engine3 = TTSEngineFactory.create_debug_engine(config)
    print(f"  ✅ 调试引擎创建: 调试模式 = {engine3.config.debug_mode}")
    
    # 测试配置验证
    valid = TTSEngineFactory.validate_config(config)
    print(f"  ✅ 配置验证: {valid}")
    
    # 测试可用引擎
    engines = TTSEngineFactory.get_available_engines()
    print(f"  ✅ 可用引擎: {len(engines)} 种")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始模块化重构测试...")
    
    try:
        success = True
        
        success &= await test_core_models()
        success &= await test_event_system()
        success &= await test_text_processor()
        success &= await test_audio_cache()
        success &= await test_adaptive_queue()
        success &= await test_modular_engine()
        success &= await test_legacy_compatibility()
        success &= await test_engine_factory()
        
        if success:
            print("\n✅ 所有模块化重构测试通过!")
            print("\n🎉 代码结构和模块化重构成功完成!")
            print("\n📋 重构成果总结:")
            print("  🏗️  模块化架构: 清晰的职责分离和接口定义")
            print("  🔌 插件化设计: 可替换的组件和扩展点")
            print("  📡 事件驱动: 解耦的事件通信机制")
            print("  🔄 向后兼容: 无缝的旧代码兼容性")
            print("  🧪 可测试性: 独立的模块便于单元测试")
            print("  📊 统计监控: 完善的性能和状态监控")
            print("  🛡️  错误处理: 统一的异常处理机制")
            print("  🎯 配置管理: 集中化的配置管理")
        else:
            print("\n❌ 部分模块化重构测试失败")
            return False
        
    except Exception as e:
        print(f"\n❌ 模块化重构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
