#!/usr/bin/env python3
"""
简化的模块化重构测试

验证重构后的核心功能是否正常工作。
"""

import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


async def test_core_imports():
    """测试核心模块导入"""
    print("🧪 测试核心模块导入...")
    
    try:
        from tts_demo.core.models import TTSConfig, PlaybackState, ProcessingStats
        print("  ✅ 核心模型导入成功")
        
        from tts_demo.core.events import SimpleEventBus, TextChunkEvent
        print("  ✅ 事件系统导入成功")
        
        from tts_demo.core.exceptions import TTSError, TextProcessingError
        print("  ✅ 异常定义导入成功")
        
        return True
    except Exception as e:
        print(f"  ❌ 核心模块导入失败: {e}")
        return False


async def test_processors():
    """测试处理器模块"""
    print("\n📝 测试处理器模块...")
    
    try:
        from tts_demo.processors import OptimizedTextProcessor, SmartTextSplitter, TextSplitterConfig
        print("  ✅ 处理器模块导入成功")
        
        # 测试文本处理器
        processor = OptimizedTextProcessor(debug_mode=False)
        filtered = processor.filter_content("这是测试```code```文本")
        print(f"  ✅ 文本过滤测试: '{filtered}'")
        
        # 测试分词
        pos = processor.segment_text("人工智能技术", 2)
        print(f"  ✅ 分词测试: 位置 = {pos}")
        
        return True
    except Exception as e:
        print(f"  ❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_audio_modules():
    """测试音频模块"""
    print("\n🔊 测试音频模块...")
    
    try:
        from tts_demo.audio import EdgeTTSGenerator, PygameAudioPlayer, SmartAudioCache
        print("  ✅ 音频模块导入成功")
        
        # 测试缓存
        cache = SmartAudioCache(max_items=5, max_memory_mb=1.0)
        stats = cache.get_stats()
        print(f"  ✅ 音频缓存测试: 当前项数 = {stats['current_items']}")
        
        return True
    except Exception as e:
        print(f"  ❌ 音频模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_queue_modules():
    """测试队列模块"""
    print("\n📋 测试队列模块...")
    
    try:
        from tts_demo.queues import AdaptiveQueueManager
        print("  ✅ 队列模块导入成功")
        
        # 测试队列
        queue = AdaptiveQueueManager(initial_size=2, max_size=5, name="test")
        await queue.put("test_item")
        size = queue.qsize()
        print(f"  ✅ 队列测试: 当前大小 = {size}")
        
        return True
    except Exception as e:
        print(f"  ❌ 队列模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_engine_factory():
    """测试引擎工厂"""
    print("\n🏭 测试引擎工厂...")
    
    try:
        from tts_demo.engines import TTSEngineFactory
        from tts_demo.core.models import TTSConfig
        print("  ✅ 引擎工厂导入成功")
        
        # 测试配置验证
        config = TTSConfig(debug_mode=True)
        valid = TTSEngineFactory.validate_config(config)
        print(f"  ✅ 配置验证: {valid}")
        
        # 测试可用引擎
        engines = TTSEngineFactory.get_available_engines()
        print(f"  ✅ 可用引擎: {len(engines)} 种")
        
        return True
    except Exception as e:
        print(f"  ❌ 引擎工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_compatibility():
    """测试兼容性适配器"""
    print("\n🔄 测试兼容性适配器...")
    
    try:
        from tts_demo.compatibility import LegacyStreamSpeaker
        print("  ✅ 兼容性适配器导入成功")
        
        # 创建兼容适配器
        speaker = LegacyStreamSpeaker(
            voice="zh-CN-YunjianNeural",
            debug_mode=True
        )
        print(f"  ✅ 适配器创建成功: 状态 = {speaker.state}")
        
        # 测试属性访问
        print(f"  ✅ 配置访问: first_chunk_size = {speaker.first_chunk_size}")
        
        return True
    except Exception as e:
        print(f"  ❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basic_functionality():
    """测试基本功能"""
    print("\n⚡ 测试基本功能...")
    
    try:
        from tts_demo.core.models import TTSConfig
        from tts_demo.engines import TTSEngineFactory
        
        # 创建配置
        config = TTSConfig(
            voice="zh-CN-YunjianNeural",
            first_chunk_size=3,
            debug_mode=True,
            enable_cache=False  # 禁用缓存简化测试
        )
        
        # 创建引擎
        engine = TTSEngineFactory.create_stream_engine(config)
        print(f"  ✅ 引擎创建成功: {type(engine).__name__}")
        
        # 测试状态
        state = engine.get_state()
        print(f"  ✅ 状态获取: {state}")
        
        # 测试统计
        stats = engine.get_stats()
        print(f"  ✅ 统计获取: 块数 = {stats.total_chunks}")
        
        return True
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 开始简化的模块化重构测试...")
    
    try:
        success = True
        
        success &= await test_core_imports()
        success &= await test_processors()
        success &= await test_audio_modules()
        success &= await test_queue_modules()
        success &= await test_engine_factory()
        success &= await test_compatibility()
        success &= await test_basic_functionality()
        
        if success:
            print("\n✅ 所有简化测试通过!")
            print("\n🎉 代码结构和模块化重构基本成功!")
            print("\n📋 重构成果:")
            print("  🏗️  模块化架构: 清晰的职责分离")
            print("  🔌 组件化设计: 可替换的组件")
            print("  📡 事件驱动: 解耦的通信机制")
            print("  🔄 向后兼容: 兼容旧代码")
            print("  🧪 可测试性: 独立的模块")
            print("  🛡️  错误处理: 统一的异常处理")
        else:
            print("\n❌ 部分测试失败")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
