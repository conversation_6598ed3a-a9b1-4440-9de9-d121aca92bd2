#!/usr/bin/env python3
"""
代码结构和模块化重构演示

展示重构后的TTS系统架构和功能。
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


def demo_text_processing():
    """演示文本处理优化"""
    print("📝 文本处理优化演示")
    print("=" * 50)
    
    try:
        from tts_demo.processors import OptimizedTextProcessor
        
        # 创建优化的文本处理器
        processor = OptimizedTextProcessor(debug_mode=False)
        
        # 演示文本过滤
        test_texts = [
            "这是一个包含```python\nprint('hello')\n```代码块的文本。",
            "还有[链接文本](http://example.com)和<b>HTML标签</b>。",
            "以及一些特殊字符：@#$%^&*()_+{}|",
            "连续标点符号：！！！？？？，，，。。。"
        ]
        
        print("🔍 文本过滤演示:")
        for i, text in enumerate(test_texts, 1):
            filtered = processor.filter_content(text)
            print(f"  {i}. 原文: {text}")
            print(f"     过滤: {filtered}")
            print()
        
        # 演示分词功能
        print("✂️ 分词功能演示:")
        test_sentences = [
            "人工智能技术正在快速发展",
            "机器学习和深度学习是重要分支",
            "自然语言处理应用广泛"
        ]
        
        for sentence in test_sentences:
            pos = processor.segment_text(sentence, 3)
            print(f"  文本: {sentence}")
            print(f"  分割位置: {pos} (前3个词)")
            print()
        
        # 演示断句位置查找
        print("🎯 断句位置查找演示:")
        text = "这是一个很长的测试文本，包含多个句子。它有逗号，分号；冒号：还有感叹号！问号？以及句号。"
        break_chars = ['。', '！', '？', '，', '；', '：']
        positions = processor.find_break_positions(text, break_chars)
        print(f"  文本: {text}")
        print(f"  断句字符: {break_chars}")
        print(f"  断句位置: {positions}")
        print()
        
        # 演示缓存效果
        print("💾 缓存效果演示:")
        cache_info = processor.get_cache_info()
        print(f"  缓存统计: 命中={cache_info['hits']}, 未命中={cache_info['misses']}")
        print(f"  命中率: {cache_info['hit_rate']:.2%}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 文本处理演示失败: {e}")
        return False


def demo_modular_architecture():
    """演示模块化架构"""
    print("🏗️ 模块化架构演示")
    print("=" * 50)
    
    try:
        # 演示核心模型
        print("📊 核心数据模型:")
        from tts_demo.core.models import TTSConfig, PlaybackState, ProcessingStats
        
        config = TTSConfig(
            voice="zh-CN-YunjianNeural",
            first_chunk_size=5,
            debug_mode=True
        )
        print(f"  ✅ TTSConfig: voice={config.voice}, chunk_size={config.first_chunk_size}")
        
        stats = ProcessingStats()
        stats.total_chunks = 10
        stats.total_characters = 100
        print(f"  ✅ ProcessingStats: chunks={stats.total_chunks}, chars={stats.total_characters}")
        print()
        
        # 演示音频缓存
        print("💾 音频缓存管理:")
        from tts_demo.audio import SmartAudioCache
        from tts_demo.core.models import AudioData
        import io
        
        cache = SmartAudioCache(max_items=5, max_memory_mb=1.0)
        
        # 创建测试音频数据
        audio_data = AudioData(
            data=io.BytesIO(b"fake audio data for demo"),
            text="测试音频"
        )
        
        # 测试缓存操作
        key = "demo_key"
        success = cache.put(key, audio_data)
        retrieved = cache.get(key)
        cache_stats = cache.get_stats()
        
        print(f"  ✅ 缓存添加: {success}")
        print(f"  ✅ 缓存获取: {retrieved is not None}")
        print(f"  ✅ 缓存统计: 项数={cache_stats['current_items']}, 命中率={cache_stats['hit_rate']:.2%}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 模块化架构演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_compatibility():
    """演示向后兼容性"""
    print("🔄 向后兼容性演示")
    print("=" * 50)
    
    try:
        # 演示重构后的StreamSpeaker
        print("🎤 重构后的StreamSpeaker:")
        from tts_demo.speaker.refactored_speaker import StreamSpeaker
        
        # 使用与原版完全相同的接口
        speaker = StreamSpeaker(
            voice="zh-CN-YunjianNeural",
            first_chunk_size=5,
            debug_mode=True,
            enable_cache=False,  # 简化演示
            on_text_chunk=lambda text: print(f"    📢 朗读回调: {text[:20]}...")
        )
        
        print(f"  ✅ 创建成功: 类型={type(speaker).__name__}")
        print(f"  ✅ 配置访问: first_chunk_size={speaker.first_chunk_size}")
        print(f"  ✅ 状态访问: state={speaker.state}")
        print(f"  ✅ 运行状态: is_running={speaker.is_running}")
        
        # 测试统计信息
        stats = speaker.get_stats()
        print(f"  ✅ 统计信息: 包含{len(stats)}个字段")
        
        # 测试兼容性方法
        filtered = speaker._filter_non_speech_content("测试```code```文本")
        print(f"  ✅ 兼容方法: 过滤结果='{filtered}'")
        
        segmented = speaker._segment_chinese_text("人工智能技术", 2)
        print(f"  ✅ 兼容方法: 分词位置={segmented}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_configuration():
    """演示配置管理"""
    print("⚙️ 配置管理演示")
    print("=" * 50)
    
    try:
        from tts_demo.core.models import TTSConfig
        
        # 演示默认配置
        print("📋 默认配置:")
        default_config = TTSConfig()
        print(f"  语音: {default_config.voice}")
        print(f"  首块大小: {default_config.first_chunk_size}")
        print(f"  缓存启用: {default_config.enable_cache}")
        print(f"  调试模式: {default_config.debug_mode}")
        print()
        
        # 演示自定义配置
        print("🎛️ 自定义配置:")
        custom_config = TTSConfig(
            voice="zh-CN-YunyangNeural",
            first_chunk_size=10,
            min_sentence_size=30,
            enable_cache=True,
            cache_size=200,
            debug_mode=True
        )
        print(f"  语音: {custom_config.voice}")
        print(f"  首块大小: {custom_config.first_chunk_size}")
        print(f"  最小句子: {custom_config.min_sentence_size}")
        print(f"  缓存大小: {custom_config.cache_size}")
        print()
        
        # 演示从旧配置创建
        print("🔄 从旧配置创建:")
        legacy_config = TTSConfig.from_legacy_config(
            voice="zh-CN-XiaoxiaoNeural",
            first_chunk_size=8,
            debug_mode=True
        )
        print(f"  语音: {legacy_config.voice}")
        print(f"  首块大小: {legacy_config.first_chunk_size}")
        print(f"  调试模式: {legacy_config.debug_mode}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主演示函数"""
    print("🚀 TTS Demo 代码结构和模块化重构演示")
    print("=" * 60)
    print()
    
    success = True
    
    # 运行各个演示
    success &= demo_text_processing()
    print()
    
    success &= demo_modular_architecture()
    print()
    
    success &= demo_compatibility()
    print()
    
    success &= demo_configuration()
    print()
    
    # 总结
    if success:
        print("🎉 重构演示完成!")
        print("=" * 60)
        print("📋 重构成果总结:")
        print("  🏗️  模块化架构: 清晰的职责分离和接口定义")
        print("  🚀 性能优化: 正则预编译、分词缓存、断句优化")
        print("  🔌 插件化设计: 可替换的组件和扩展点")
        print("  📡 事件驱动: 解耦的事件通信机制")
        print("  🔄 向后兼容: 无缝的旧代码兼容性")
        print("  🧪 可测试性: 独立的模块便于单元测试")
        print("  📊 统计监控: 完善的性能和状态监控")
        print("  🛡️  错误处理: 统一的异常处理机制")
        print("  🎯 配置管理: 集中化的配置管理")
        print()
        print("📖 详细文档: docs/CODE_STRUCTURE_REFACTOR.md")
    else:
        print("❌ 部分演示失败，请检查依赖和配置")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
