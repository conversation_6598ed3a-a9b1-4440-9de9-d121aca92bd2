#!/usr/bin/env python3
"""
简单测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        import psutil
        print(f"✅ psutil 导入成功，版本: {psutil.__version__}")
    except Exception as e:
        print(f"❌ psutil 导入失败: {e}")
        return False
    
    try:
        from tts_demo.config.models import QueueConfig, CacheConfig
        print("✅ 配置模型导入成功")
    except Exception as e:
        print(f"❌ 配置模型导入失败: {e}")
        return False
    
    try:
        from tts_demo.speaker.stream_speaker import MemoryStats, QueueStats
        print("✅ 统计类导入成功")
    except Exception as e:
        print(f"❌ 统计类导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        from tts_demo.speaker.stream_speaker import MemoryStats, QueueStats
        
        # 测试内存统计
        memory_stats = MemoryStats()
        memory_stats.update_memory_usage(50 * 1024 * 1024, 10)  # 50MB, 10项
        print(f"✅ 内存统计: {memory_stats.cache_memory_mb:.1f}MB, {memory_stats.cache_items_count}项")
        
        # 测试队列统计
        queue_stats = QueueStats()
        queue_stats.tts_queue_size = 5
        queue_stats.audio_queue_size = 3
        print(f"✅ 队列统计: TTS={queue_stats.tts_queue_size}, Audio={queue_stats.audio_queue_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单测试...")
    
    if not test_imports():
        print("❌ 导入测试失败，停止测试")
        return
    
    if not test_basic_functionality():
        print("❌ 功能测试失败")
        return
    
    print("\n✅ 所有测试通过!")

if __name__ == "__main__":
    main()
