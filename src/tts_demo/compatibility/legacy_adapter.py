"""
旧版StreamSpeaker兼容适配器

提供与原始StreamSpeaker完全兼容的接口，内部使用新的模块化引擎。
"""

import asyncio
import logging
from typing import AsyncGenerator, Optional, Callable, Any, List, Dict, Union

from ..core.models import PlaybackState, ProcessingStats, TTSConfig
from ..config.models import FirstChunkBreakMode
from ..engines import TTSEngineFactory, ModularStreamTTSEngine

logger = logging.getLogger(__name__)


class LegacyStreamSpeaker:
    """旧版StreamSpeaker兼容适配器
    
    提供与原始StreamSpeaker完全相同的接口，内部使用新的模块化引擎。
    这样可以让现有代码无需修改就能使用新的架构。
    """
    
    def __init__(
        self,
        voice: Optional[str] = None,
        rate: Optional[str] = None,
        volume: Optional[str] = None,
        first_chunk_size: int = 5,
        min_sentence_size: int = 20,
        max_chunk_size: int = 500,
        on_text_chunk: Optional[Callable[[str], Any]] = None,
        on_state_change: Optional[Callable[[PlaybackState], Any]] = None,
        on_stats_update: Optional[Callable[[Dict[str, Any]], Any]] = None,
        audio_frequency: int = 24000,
        queue_size: int = 10,
        max_queue_size: int = 50,
        enable_cache: bool = True,
        cache_size: int = 100,
        cache_memory_mb: float = 100.0,
        custom_sentence_ends: Optional[List[str]] = None,
        custom_clause_breaks: Optional[List[str]] = None,
        custom_other_breaks: Optional[List[str]] = None,
        stats_update_interval: float = 5.0,
        retry_attempts: int = 3,
        retry_delay: float = 0.5,
        first_chunk_break_mode: Union[FirstChunkBreakMode, str] = FirstChunkBreakMode.PUNCTUATION,
        debug_mode: bool = False,
    ):
        """初始化兼容适配器
        
        参数与原始StreamSpeaker完全相同，确保向后兼容。
        """
        # 保存回调函数
        self.on_text_chunk = on_text_chunk
        self.on_state_change = on_state_change
        self.on_stats_update = on_stats_update
        
        # 创建配置
        self.config = TTSConfig(
            voice=voice or "zh-CN-YunjianNeural",
            rate=rate or "+0%",
            volume=volume or "+0%",
            first_chunk_size=first_chunk_size,
            min_sentence_size=min_sentence_size,
            max_chunk_size=max_chunk_size,
            queue_size=queue_size,
            max_queue_size=max_queue_size,
            enable_cache=enable_cache,
            cache_size=cache_size,
            cache_memory_mb=cache_memory_mb,
            debug_mode=debug_mode,
            audio_frequency=audio_frequency,
            stats_update_interval=stats_update_interval,
            retry_attempts=retry_attempts,
            retry_delay=retry_delay
        )
        
        # 处理断句模式
        if isinstance(first_chunk_break_mode, str):
            self.first_chunk_break_mode = FirstChunkBreakMode(first_chunk_break_mode)
        else:
            self.first_chunk_break_mode = first_chunk_break_mode
        
        # 保存断句配置（暂时未使用，保持兼容性）
        self._sentence_ends = custom_sentence_ends or ['。', '！', '？', '…', '\n\n']
        self._clause_breaks = custom_clause_breaks or ['，', '；', '：', ',', ';', ':']
        self._other_breaks = custom_other_breaks or [' ', '、', '(', '（', ')', '）']
        
        # 创建内部引擎
        self._engine: Optional[ModularStreamTTSEngine] = None
        self._create_engine()
        
        logger.debug("LegacyStreamSpeaker 兼容适配器初始化完成")
    
    def _create_engine(self):
        """创建内部引擎"""
        # 准备事件处理器
        event_handlers = {}
        
        if self.on_text_chunk:
            def text_chunk_handler(event):
                self.on_text_chunk(event.chunk.content)
            event_handlers['text_chunk'] = text_chunk_handler
        
        if self.on_state_change:
            def state_change_handler(event):
                self.on_state_change(event.new_state)
            event_handlers['playback_state'] = state_change_handler
        
        if self.on_stats_update:
            def stats_update_handler(event):
                self.on_stats_update(event.stats.to_dict())
            event_handlers['stats_update'] = stats_update_handler
        
        # 创建引擎
        self._engine = TTSEngineFactory.create_stream_engine(
            self.config,
            event_handlers=event_handlers
        )
    
    async def start_stream_processing(self, stream: AsyncGenerator[Any, None], 
                                    content_extractor: Callable[[Any], Optional[str]]):
        """开始流式处理
        
        Args:
            stream: 异步生成器流
            content_extractor: 内容提取函数
        """
        if not self._engine:
            raise RuntimeError("引擎未初始化")
        
        try:
            await self._engine.start()
            await self._engine.process_stream(stream, content_extractor)
        finally:
            await self._engine.stop()
    
    def stop(self):
        """停止处理"""
        if self._engine:
            # 创建一个任务来停止引擎
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._engine.stop())
            else:
                loop.run_until_complete(self._engine.stop())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self._engine:
            return {}
        
        stats = self._engine.get_stats()
        return stats.to_dict()
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        if not self._engine:
            return {}
        
        return self._engine.get_component_stats()
    
    @property
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._engine.is_running if self._engine else False
    
    @property
    def state(self) -> PlaybackState:
        """获取当前状态"""
        return self._engine.get_state() if self._engine else PlaybackState.STOPPED
    
    # 兼容性属性和方法
    @property
    def first_chunk_size(self) -> int:
        return self.config.first_chunk_size
    
    @first_chunk_size.setter
    def first_chunk_size(self, value: int):
        self.config.first_chunk_size = value
        self._recreate_engine()
    
    @property
    def min_sentence_size(self) -> int:
        return self.config.min_sentence_size
    
    @min_sentence_size.setter
    def min_sentence_size(self, value: int):
        self.config.min_sentence_size = value
        self._recreate_engine()
    
    @property
    def max_chunk_size(self) -> int:
        return self.config.max_chunk_size
    
    @max_chunk_size.setter
    def max_chunk_size(self, value: int):
        self.config.max_chunk_size = value
        self._recreate_engine()
    
    def _recreate_engine(self):
        """重新创建引擎（当配置改变时）"""
        if self._engine and self._engine.is_running:
            logger.warning("引擎正在运行，无法重新创建")
            return
        
        self._create_engine()
    
    # 为了完全兼容，添加一些可能被外部代码使用的属性
    @property
    def buffer(self) -> str:
        """缓冲区内容（只读）"""
        return self._engine.buffer if self._engine else ""
    
    @property
    def first_chunk(self) -> bool:
        """是否为首块（只读）"""
        return self._engine.first_chunk if self._engine else True
    
    def _filter_non_speech_content(self, text: str) -> str:
        """过滤非语音内容（兼容方法）"""
        if self._engine and self._engine.text_processor:
            return self._engine.text_processor.filter_content(text)
        return text
    
    def _segment_chinese_text(self, text: str, max_words: int) -> int:
        """中文分词（兼容方法）"""
        if self._engine and self._engine.text_processor:
            return self._engine.text_processor.segment_text(text, max_words)
        return len(text)
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if self._engine and self._engine.is_running:
                self.stop()
        except Exception as e:
            logger.debug(f"清理LegacyStreamSpeaker时出错: {e}")
