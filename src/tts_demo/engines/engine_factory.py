"""
TTS引擎工厂

提供创建和配置TTS引擎的工厂方法。
"""

import logging
from typing import Optional, Dict, Any, Callable

from ..core.interfaces import TTSEngine, TextProcessor, AudioGenerator, AudioPlayer, CacheManager, EventBus
from ..core.models import TTSConfig
from ..core.events import SimpleEventBus
from ..processors import OptimizedTextProcessor
from ..audio import EdgeTTSGenerator, PygameAudioPlayer, SmartAudioCache
from .stream_engine import ModularStreamTTSEngine

logger = logging.getLogger(__name__)


class TTSEngineFactory:
    """TTS引擎工厂"""
    
    @staticmethod
    def create_stream_engine(config: TTSConfig, 
                           custom_components: Optional[Dict[str, Any]] = None,
                           event_handlers: Optional[Dict[str, Callable]] = None) -> ModularStreamTTSEngine:
        """创建流式TTS引擎
        
        Args:
            config: TTS配置
            custom_components: 自定义组件字典，可包含：
                - text_processor: 自定义文本处理器
                - audio_generator: 自定义音频生成器
                - audio_player: 自定义音频播放器
                - cache_manager: 自定义缓存管理器
                - event_bus: 自定义事件总线
            event_handlers: 事件处理器字典，键为事件类型，值为处理函数
            
        Returns:
            配置好的TTS引擎实例
        """
        custom_components = custom_components or {}
        
        # 创建组件
        text_processor = custom_components.get('text_processor') or OptimizedTextProcessor(
            debug_mode=config.debug_mode,
            cache_size=config.cache_size
        )
        
        audio_generator = custom_components.get('audio_generator') or EdgeTTSGenerator(
            retry_attempts=config.retry_attempts,
            retry_delay=config.retry_delay
        )
        
        audio_player = custom_components.get('audio_player') or PygameAudioPlayer(
            frequency=config.audio_frequency
        )
        
        cache_manager = None
        if config.enable_cache:
            cache_manager = custom_components.get('cache_manager') or SmartAudioCache(
                max_items=config.cache_size,
                max_memory_mb=config.cache_memory_mb
            )
        
        event_bus = custom_components.get('event_bus') or SimpleEventBus()
        
        # 创建引擎
        engine = ModularStreamTTSEngine(
            config=config,
            text_processor=text_processor,
            audio_generator=audio_generator,
            audio_player=audio_player,
            cache_manager=cache_manager,
            event_bus=event_bus
        )
        
        # 注册事件处理器
        if event_handlers:
            for event_type, handler in event_handlers.items():
                engine.add_event_handler(event_type, handler)
        
        logger.info(f"创建流式TTS引擎完成，配置: {config}")
        return engine
    
    @staticmethod
    def create_from_legacy_config(**kwargs) -> ModularStreamTTSEngine:
        """从旧版配置创建TTS引擎
        
        Args:
            **kwargs: 旧版配置参数
            
        Returns:
            TTS引擎实例
        """
        # 转换为新配置格式
        config = TTSConfig.from_legacy_config(**kwargs)
        
        # 提取回调函数作为事件处理器
        event_handlers = {}
        
        if 'on_text_chunk' in kwargs and kwargs['on_text_chunk']:
            def text_chunk_handler(event):
                kwargs['on_text_chunk'](event.chunk.content)
            event_handlers['text_chunk'] = text_chunk_handler
        
        if 'on_state_change' in kwargs and kwargs['on_state_change']:
            def state_change_handler(event):
                kwargs['on_state_change'](event.new_state)
            event_handlers['playback_state'] = state_change_handler
        
        if 'on_stats_update' in kwargs and kwargs['on_stats_update']:
            def stats_update_handler(event):
                kwargs['on_stats_update'](event.stats.to_dict())
            event_handlers['stats_update'] = stats_update_handler
        
        return TTSEngineFactory.create_stream_engine(config, event_handlers=event_handlers)
    
    @staticmethod
    def create_with_custom_text_processor(config: TTSConfig, 
                                        text_processor: TextProcessor) -> ModularStreamTTSEngine:
        """创建使用自定义文本处理器的TTS引擎"""
        return TTSEngineFactory.create_stream_engine(
            config, 
            custom_components={'text_processor': text_processor}
        )
    
    @staticmethod
    def create_with_custom_audio_generator(config: TTSConfig, 
                                         audio_generator: AudioGenerator) -> ModularStreamTTSEngine:
        """创建使用自定义音频生成器的TTS引擎"""
        return TTSEngineFactory.create_stream_engine(
            config, 
            custom_components={'audio_generator': audio_generator}
        )
    
    @staticmethod
    def create_with_custom_audio_player(config: TTSConfig, 
                                      audio_player: AudioPlayer) -> ModularStreamTTSEngine:
        """创建使用自定义音频播放器的TTS引擎"""
        return TTSEngineFactory.create_stream_engine(
            config, 
            custom_components={'audio_player': audio_player}
        )
    
    @staticmethod
    def create_without_cache(config: TTSConfig) -> ModularStreamTTSEngine:
        """创建不使用缓存的TTS引擎"""
        config_no_cache = TTSConfig(
            **{k: v for k, v in config.__dict__.items() if k != 'enable_cache'},
            enable_cache=False
        )
        return TTSEngineFactory.create_stream_engine(config_no_cache)
    
    @staticmethod
    def create_debug_engine(config: TTSConfig) -> ModularStreamTTSEngine:
        """创建调试模式的TTS引擎，包含详细的事件处理器"""
        
        def debug_text_chunk_handler(event):
            logger.info(f"[DEBUG] 文本块: '{event.chunk.content[:50]}...' (类型: {event.chunk.break_type})")
        
        def debug_audio_generated_handler(event):
            logger.info(f"[DEBUG] 音频生成: '{event.text[:30]}...' (大小: {event.audio_data.get_size_bytes()} bytes)")
        
        def debug_playback_state_handler(event):
            logger.info(f"[DEBUG] 播放状态变化: {event.old_state} -> {event.new_state}")
        
        def debug_stats_update_handler(event):
            stats = event.stats
            logger.info(f"[DEBUG] 统计更新: 块数={stats.total_chunks}, 字符数={stats.total_characters}, "
                       f"缓存命中率={stats.cache_hit_rate:.2%}")
        
        def debug_error_handler(event):
            logger.error(f"[DEBUG] 错误事件: {event.error} (上下文: {event.context})")
        
        def debug_cache_handler(event):
            logger.debug(f"[DEBUG] 缓存事件: {event.action} (键: {event.key[:8] if event.key else 'N/A'}...)")
        
        event_handlers = {
            'text_chunk': debug_text_chunk_handler,
            'audio_generated': debug_audio_generated_handler,
            'playback_state': debug_playback_state_handler,
            'stats_update': debug_stats_update_handler,
            'error': debug_error_handler,
            'cache': debug_cache_handler
        }
        
        # 确保调试模式开启
        debug_config = TTSConfig(
            **{k: v for k, v in config.__dict__.items() if k != 'debug_mode'},
            debug_mode=True
        )
        
        return TTSEngineFactory.create_stream_engine(debug_config, event_handlers=event_handlers)
    
    @staticmethod
    def get_available_engines() -> Dict[str, str]:
        """获取可用的引擎类型"""
        return {
            'stream': 'ModularStreamTTSEngine - 模块化流式TTS引擎',
            'legacy': 'StreamSpeaker - 兼容旧版的TTS引擎'
        }
    
    @staticmethod
    def validate_config(config: TTSConfig) -> bool:
        """验证配置是否有效"""
        try:
            # 基本验证
            if config.first_chunk_size <= 0:
                logger.error("first_chunk_size 必须大于0")
                return False
            
            if config.min_sentence_size <= 0:
                logger.error("min_sentence_size 必须大于0")
                return False
            
            if config.max_chunk_size <= config.min_sentence_size:
                logger.error("max_chunk_size 必须大于 min_sentence_size")
                return False
            
            if config.queue_size <= 0 or config.max_queue_size <= 0:
                logger.error("队列大小必须大于0")
                return False
            
            if config.max_queue_size < config.queue_size:
                logger.error("max_queue_size 必须大于等于 queue_size")
                return False
            
            if config.cache_size <= 0 or config.cache_memory_mb <= 0:
                logger.error("缓存配置必须大于0")
                return False
            
            if config.audio_frequency <= 0:
                logger.error("音频采样率必须大于0")
                return False
            
            if config.retry_attempts < 0 or config.retry_delay < 0:
                logger.error("重试配置不能为负数")
                return False
            
            logger.debug("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
