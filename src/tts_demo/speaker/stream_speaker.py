"""
流式文本转语音处理器模块

该模块提供了一个StreamSpeaker类，用于将大模型的流式文本输出实时转换为语音。
支持自定义语音、语速、音量，以及灵活的文本分块和断句策略。
"""

import asyncio
import re
import os
import io
import time
import hashlib
import logging
import psutil
import threading
from enum import Enum
from typing import AsyncGenerator, Optional, Callable, Any, List, Dict, Union
from dataclasses import dataclass, field
from collections import OrderedDict
from dotenv import load_dotenv

import edge_tts
import pygame
from tts_demo.config.models import FirstChunkBreakMode

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("StreamSpeaker")

# 导入jieba分词库
import jieba
import os

# 添加自定义词典
custom_dict_path = os.path.join(os.path.dirname(__file__), "custom_dict.txt")

# 加载自定义词典
if os.path.exists(custom_dict_path):
    jieba.load_userdict(custom_dict_path)
    logger.info(f"已加载自定义词典: {custom_dict_path}")

# 加载环境变量
load_dotenv()


class BreakType(Enum):
    """断句类型枚举"""
    SENTENCE = 1  # 完整句子（句号、感叹号、问号等）
    CLAUSE = 2    # 从句（逗号、分号、冒号等）
    FORCED = 3    # 强制断句（达到最大长度）
    FIRST_CHUNK = 4  # 第一个文本块


class PlaybackState(Enum):
    """播放状态枚举"""
    STOPPED = 0   # 停止状态
    PLAYING = 1   # 播放状态
    PAUSED = 2    # 暂停状态


@dataclass
class MemoryStats:
    """内存使用统计"""
    cache_memory_mb: float = 0.0      # 缓存占用内存（MB）
    process_memory_mb: float = 0.0    # 进程总内存（MB）
    system_memory_percent: float = 0.0  # 系统内存使用率
    cache_items_count: int = 0        # 缓存项数量
    max_cache_memory_mb: float = 100.0  # 最大缓存内存限制（MB）

    def update_memory_usage(self, cache_size_bytes: int, cache_count: int):
        """更新内存使用情况"""
        self.cache_memory_mb = cache_size_bytes / (1024 * 1024)
        self.cache_items_count = cache_count

        # 获取进程内存使用
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            self.process_memory_mb = memory_info.rss / (1024 * 1024)

            # 获取系统内存使用率
            system_memory = psutil.virtual_memory()
            self.system_memory_percent = system_memory.percent
        except Exception:
            pass  # 忽略获取内存信息的错误


@dataclass
class QueueStats:
    """队列统计信息"""
    tts_queue_size: int = 0           # TTS队列当前大小
    audio_queue_size: int = 0         # 音频队列当前大小
    tts_queue_max_size: int = 10      # TTS队列最大大小
    audio_queue_max_size: int = 10    # 音频队列最大大小
    tts_queue_full_count: int = 0     # TTS队列满的次数
    audio_queue_full_count: int = 0   # 音频队列满的次数
    total_enqueued: int = 0           # 总入队数量
    total_dequeued: int = 0           # 总出队数量


@dataclass
class TTSStats:
    """TTS统计信息"""
    total_chunks: int = 0          # 处理的文本块总数
    total_characters: int = 0      # 处理的字符总数
    total_audio_time: float = 0.0  # 生成的音频总时长（秒）
    cache_hits: int = 0            # 缓存命中次数
    cache_misses: int = 0          # 缓存未命中次数
    start_time: float = 0.0        # 开始时间
    processing_time: float = 0.0   # 处理时间
    first_token_time: float = 0.0  # 收到第一个token的时间
    first_audio_time: float = 0.0  # 开始播放第一个音频的时间
    time_to_first_audio: float = 0.0  # 从收到第一个token到开始播放第一个音频的时间
    memory_stats: MemoryStats = field(default_factory=MemoryStats)  # 内存统计
    queue_stats: QueueStats = field(default_factory=QueueStats)     # 队列统计

    @property
    def elapsed_time(self) -> float:
        """获取已经过的时间（秒）"""
        if self.start_time == 0:
            return 0.0
        return time.time() - self.start_time

    @property
    def chars_per_second(self) -> float:
        """获取每秒处理的字符数"""
        if self.elapsed_time == 0:
            return 0.0
        return self.total_characters / self.elapsed_time

    @property
    def cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.cache_hits + self.cache_misses
        if total == 0:
            return 0.0
        return self.cache_hits / total

    def reset(self):
        """重置统计信息"""
        self.total_chunks = 0
        self.total_characters = 0
        self.total_audio_time = 0.0
        self.cache_hits = 0
        self.cache_misses = 0
        self.start_time = time.time()
        self.processing_time = 0.0
        self.first_token_time = 0.0
        self.first_audio_time = 0.0
        self.time_to_first_audio = 0.0
        self.memory_stats = MemoryStats()
        self.queue_stats = QueueStats()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_chunks": self.total_chunks,
            "total_characters": self.total_characters,
            "total_audio_time": self.total_audio_time,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "elapsed_time": self.elapsed_time,
            "chars_per_second": self.chars_per_second,
            "cache_hit_rate": self.cache_hit_rate,
            "processing_time": self.processing_time,
            "first_token_time": self.first_token_time,
            "first_audio_time": self.first_audio_time,
            "time_to_first_audio": self.time_to_first_audio,
            "memory_stats": {
                "cache_memory_mb": self.memory_stats.cache_memory_mb,
                "process_memory_mb": self.memory_stats.process_memory_mb,
                "system_memory_percent": self.memory_stats.system_memory_percent,
                "cache_items_count": self.memory_stats.cache_items_count,
                "max_cache_memory_mb": self.memory_stats.max_cache_memory_mb
            },
            "queue_stats": {
                "tts_queue_size": self.queue_stats.tts_queue_size,
                "audio_queue_size": self.queue_stats.audio_queue_size,
                "tts_queue_max_size": self.queue_stats.tts_queue_max_size,
                "audio_queue_max_size": self.queue_stats.audio_queue_max_size,
                "tts_queue_full_count": self.queue_stats.tts_queue_full_count,
                "audio_queue_full_count": self.queue_stats.audio_queue_full_count,
                "total_enqueued": self.queue_stats.total_enqueued,
                "total_dequeued": self.queue_stats.total_dequeued
            }
        }


class SmartAudioCache:
    """智能音频缓存管理器"""

    def __init__(self, max_items: int = 100, max_memory_mb: float = 100.0):
        self.max_items = max_items
        self.max_memory_mb = max_memory_mb
        self._cache = OrderedDict()
        self._cache_size_bytes = 0
        self._lock = threading.Lock()

    def get_cache_size_bytes(self) -> int:
        """获取缓存占用的字节数"""
        return self._cache_size_bytes

    def get_cache_count(self) -> int:
        """获取缓存项数量"""
        return len(self._cache)

    def _estimate_audio_size(self, audio_data: io.BytesIO) -> int:
        """估算音频数据大小"""
        current_pos = audio_data.tell()
        audio_data.seek(0, 2)  # 移动到末尾
        size = audio_data.tell()
        audio_data.seek(current_pos)  # 恢复原位置
        return size

    def _cleanup_cache(self):
        """清理缓存以满足内存限制"""
        current_memory_mb = self._cache_size_bytes / (1024 * 1024)

        # 如果超过内存限制，移除最旧的项
        while (current_memory_mb > self.max_memory_mb or
               len(self._cache) > self.max_items) and self._cache:

            key, audio_data = self._cache.popitem(last=False)
            removed_size = self._estimate_audio_size(audio_data)
            self._cache_size_bytes -= removed_size
            current_memory_mb = self._cache_size_bytes / (1024 * 1024)

            logger.debug(f"清理缓存项: {key[:8]}..., 释放内存: {removed_size/1024:.1f}KB")

    def put(self, key: str, audio_data: io.BytesIO) -> bool:
        """添加到缓存"""
        with self._lock:
            # 估算音频大小
            audio_size = self._estimate_audio_size(audio_data)

            # 检查单个音频是否超过内存限制
            if audio_size / (1024 * 1024) > self.max_memory_mb:
                logger.warning(f"音频数据过大({audio_size/1024/1024:.1f}MB)，跳过缓存")
                return False

            # 如果键已存在，先移除旧的
            if key in self._cache:
                old_audio = self._cache[key]
                old_size = self._estimate_audio_size(old_audio)
                self._cache_size_bytes -= old_size
                del self._cache[key]

            # 复制音频数据
            audio_data.seek(0)
            cached_data = io.BytesIO(audio_data.getvalue())
            audio_data.seek(0)

            # 添加到缓存
            self._cache[key] = cached_data
            self._cache_size_bytes += audio_size

            # 清理缓存
            self._cleanup_cache()

            return True

    def get(self, key: str) -> Optional[io.BytesIO]:
        """从缓存获取"""
        with self._lock:
            if key in self._cache:
                cached_data = self._cache[key]

                # 复制音频数据
                cached_data.seek(0)
                audio_data = io.BytesIO(cached_data.getvalue())
                cached_data.seek(0)

                # 更新缓存顺序（最近使用）
                self._cache.move_to_end(key)

                return audio_data

            return None

    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._cache_size_bytes = 0


class AdaptiveQueue:
    """自适应队列管理器"""

    def __init__(self, initial_size: int = 10, max_size: int = 50):
        self.initial_size = initial_size
        self.max_size = max_size
        self.current_max_size = initial_size
        self._queue = asyncio.Queue(maxsize=initial_size)
        self._full_count = 0
        self._total_enqueued = 0
        self._total_dequeued = 0
        self._lock = asyncio.Lock()

    async def put(self, item: Any) -> bool:
        """放入队列，如果队列满则尝试扩容"""
        async with self._lock:
            try:
                self._queue.put_nowait(item)
                self._total_enqueued += 1
                return True
            except asyncio.QueueFull:
                self._full_count += 1

                # 尝试扩容
                if self.current_max_size < self.max_size:
                    await self._expand_queue()
                    try:
                        self._queue.put_nowait(item)
                        self._total_enqueued += 1
                        return True
                    except asyncio.QueueFull:
                        pass

                # 如果仍然满，则阻塞等待
                await self._queue.put(item)
                self._total_enqueued += 1
                return True

    async def get(self) -> Any:
        """从队列获取"""
        item = await self._queue.get()
        self._total_dequeued += 1
        return item

    def get_nowait(self) -> Any:
        """非阻塞获取"""
        item = self._queue.get_nowait()
        self._total_dequeued += 1
        return item

    def task_done(self):
        """标记任务完成"""
        self._queue.task_done()

    async def join(self):
        """等待所有任务完成"""
        await self._queue.join()

    def qsize(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()

    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()

    def full(self) -> bool:
        """检查队列是否满"""
        return self._queue.full()

    async def _expand_queue(self):
        """扩容队列"""
        new_size = min(self.current_max_size * 2, self.max_size)
        if new_size > self.current_max_size:
            # 创建新队列
            new_queue = asyncio.Queue(maxsize=new_size)

            # 迁移现有项目
            while not self._queue.empty():
                try:
                    item = self._queue.get_nowait()
                    new_queue.put_nowait(item)
                except (asyncio.QueueEmpty, asyncio.QueueFull):
                    break

            self._queue = new_queue
            self.current_max_size = new_size
            logger.info(f"队列已扩容至: {new_size}")

    def get_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        return {
            "current_size": self.qsize(),
            "max_size": self.current_max_size,
            "full_count": self._full_count,
            "total_enqueued": self._total_enqueued,
            "total_dequeued": self._total_dequeued
        }


class StreamSpeaker:
    """
    流式文本转语音处理器，支持大模型的增量输出

    主要功能：
    1. 实时处理流式文本输入
    2. 智能断句，保持语音流畅自然
    3. 快速响应，第一个文本块迅速开始朗读
    4. 过滤不适合朗读的内容
    """

    def __init__(
        self,
        voice: Optional[str] = None,
        rate: Optional[str] = None,
        volume: Optional[str] = None,
        first_chunk_size: int = 5,  # 第一个文本块的大小
        min_sentence_size: int = 20,  # 最小句子大小，用于非首个文本块
        max_chunk_size: int = 500,  # 最大处理块大小，防止过长句子
        on_text_chunk: Optional[Callable[[str], Any]] = None,
        on_state_change: Optional[Callable[[PlaybackState], Any]] = None,
        on_stats_update: Optional[Callable[[Dict[str, Any]], Any]] = None,
        audio_frequency: int = 24000,  # 音频采样率
        queue_size: int = 10,  # 队列初始大小
        max_queue_size: int = 50,  # 队列最大大小
        enable_cache: bool = True,  # 是否启用缓存
        cache_size: int = 100,  # 缓存项数量限制
        cache_memory_mb: float = 100.0,  # 缓存内存限制（MB）
        custom_sentence_ends: Optional[List[str]] = None,  # 自定义句子结束标记
        custom_clause_breaks: Optional[List[str]] = None,  # 自定义从句断句点
        custom_other_breaks: Optional[List[str]] = None,  # 自定义其他分割点
        stats_update_interval: float = 5.0,  # 统计信息更新间隔（秒）
        retry_attempts: int = 3,  # 重试次数
        retry_delay: float = 0.5,  # 重试延迟（秒）
        first_chunk_break_mode: Union[FirstChunkBreakMode, str] = FirstChunkBreakMode.PUNCTUATION,  # 首块断句模式
    ):
        """初始化流式语音合成器

        参数:
            voice: TTS语音名称
            rate: 语速
            volume: 音量
            first_chunk_size: 第一个文本块的最大词数，仅在分词方式断句时使用
            min_sentence_size: 最小句子大小，用于非首个文本块
            max_chunk_size: 最大处理块大小，防止过长句子
            on_text_chunk: 文本块处理回调函数
            on_state_change: 状态变化回调函数
            on_stats_update: 统计信息更新回调函数
            audio_frequency: 音频采样率
            queue_size: 队列大小
            enable_cache: 是否启用缓存
            cache_size: 缓存大小
            custom_sentence_ends: 自定义句子结束标记
            custom_clause_breaks: 自定义从句断句点
            custom_other_breaks: 自定义其他分割点
            stats_update_interval: 统计信息更新间隔（秒）
            retry_attempts: 重试次数
            retry_delay: 重试延迟（秒）
            first_chunk_break_mode: 首块断句模式，可选值：WORD_SEGMENTATION（分词方式）或PUNCTUATION（标点符号方式）
        """
        # TTS配置
        self.voice = voice or os.environ.get("DEFAULT_TTS_VOICE", "zh-CN-YunjianNeural")
        self.rate = rate or os.environ.get("DEFAULT_TTS_RATE", "+0%")
        self.volume = volume or os.environ.get("DEFAULT_TTS_VOLUME", "+0%")

        # 文本处理配置
        self.first_chunk_size = first_chunk_size
        self.min_sentence_size = min_sentence_size
        self.max_chunk_size = max_chunk_size
        self.audio_frequency = audio_frequency

        # 处理首块断句模式
        if isinstance(first_chunk_break_mode, str) and not isinstance(first_chunk_break_mode, FirstChunkBreakMode):
            try:
                self.first_chunk_break_mode = FirstChunkBreakMode(first_chunk_break_mode)
            except ValueError:
                logger.warning(f"无效的首块断句模式: {first_chunk_break_mode}，使用默认值: {FirstChunkBreakMode.WORD_SEGMENTATION}")
                self.first_chunk_break_mode = FirstChunkBreakMode.WORD_SEGMENTATION
        else:
            self.first_chunk_break_mode = first_chunk_break_mode

        # 断句模式配置
        self._sentence_ends = custom_sentence_ends or ['。', '！', '？', '…', '\n\n']  # 句子结束标记
        self._clause_breaks = custom_clause_breaks or ['，', '；', '：', ',', ';', ':']  # 从句断句点
        self._other_breaks = custom_other_breaks or [' ', '、', '(', '（', ')', '）']  # 其他可能的分割点

        # 回调函数
        self.on_text_chunk = on_text_chunk
        self.on_state_change = on_state_change
        self.on_stats_update = on_stats_update

        # 缓存配置
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        self.cache_memory_mb = cache_memory_mb
        self._audio_cache = SmartAudioCache(max_items=cache_size, max_memory_mb=cache_memory_mb)

        # 错误恢复配置
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay

        # 状态变量
        self.buffer = ""
        self.is_running = False
        self.first_chunk = True
        self.playback_state = PlaybackState.STOPPED
        self.stats = TTSStats()
        self.stats_update_interval = stats_update_interval

        # 任务和队列 - 使用自适应队列
        self.tts_queue = AdaptiveQueue(initial_size=queue_size, max_size=max_queue_size)
        self.audio_queue = AdaptiveQueue(initial_size=queue_size, max_size=max_queue_size)
        self.processing_task = None
        self.current_playing = None
        self.speaking_task = None
        self.stats_task = None
        self.memory_monitor_task = None

    async def start(self):
        """启动TTS处理循环"""
        if self.is_running:
            logger.warning("StreamSpeaker已经在运行中")
            return

        logger.info(f"启动StreamSpeaker，语音={self.voice}，语速={self.rate}，音量={self.volume}")
        self.is_running = True

        # 重置统计信息
        self.stats.reset()

        # 初始化音频播放器
        pygame.mixer.init(frequency=self.audio_frequency)

        # 启动工作线程
        self.processing_task = asyncio.create_task(self._process_worker())
        self.speaking_task = asyncio.create_task(self._audio_player())

        # 启动统计信息更新任务
        if self.on_stats_update:
            self.stats_task = asyncio.create_task(self._stats_updater())

        # 启动内存监控任务
        self.memory_monitor_task = asyncio.create_task(self._memory_monitor())

        # 预热TTS引擎
        asyncio.create_task(self._warmup_tts("准备"))

        # 更新状态
        self._update_playback_state(PlaybackState.PLAYING)

    async def stop(self):
        """停止TTS处理"""
        if not self.is_running:
            return

        logger.info("停止StreamSpeaker")
        self.is_running = False

        # 取消所有任务
        for task in [self.processing_task, self.speaking_task, self.stats_task, self.memory_monitor_task]:
            if task and not task.done():
                task.cancel()

        # 停止当前播放
        if self.current_playing:
            pygame.mixer.music.stop()

        # 清理资源
        pygame.mixer.quit()

        # 清空队列
        while not self.tts_queue.empty():
            try:
                self.tts_queue.get_nowait()
                self.tts_queue.task_done()
            except:
                break

        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
                self.audio_queue.task_done()
            except:
                break

        # 清空缓存
        self._audio_cache.clear()

        # 更新状态
        self._update_playback_state(PlaybackState.STOPPED)

    async def pause(self):
        """暂停播放"""
        if self.playback_state != PlaybackState.PLAYING:
            return

        logger.info("暂停播放")
        pygame.mixer.music.pause()
        self._update_playback_state(PlaybackState.PAUSED)

    async def resume(self):
        """恢复播放"""
        if self.playback_state != PlaybackState.PAUSED:
            return

        logger.info("恢复播放")
        pygame.mixer.music.unpause()
        self._update_playback_state(PlaybackState.PLAYING)

    def set_volume(self, volume: float):
        """设置音量

        参数:
            volume: 音量值（0.0-1.0）
        """
        pygame.mixer.music.set_volume(volume)
        logger.info(f"设置音量: {volume}")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.to_dict()

    def add_custom_word(self, word: str, freq: int = 5) -> None:
        """添加自定义词语到jieba分词词典

        参数:
            word: 要添加的词语
            freq: 词频，默认为5
        """
        # 检查词语是否已存在于词典文件中
        if os.path.exists(custom_dict_path):
            with open(custom_dict_path, "r", encoding="utf-8") as f:
                existing_words = [line.split()[0] for line in f.readlines() if line.strip()]

            if word in existing_words:
                logger.info(f"词语 '{word}' 已存在于自定义词典中")
                return

        # 添加词语到内存中的词典
        jieba.add_word(word, freq)

        # 同时添加到文件中的自定义词典
        with open(custom_dict_path, "a", encoding="utf-8") as f:
            f.write(f"{word} {freq}\n")

        logger.info(f"已添加自定义词语: {word}")

    def _update_playback_state(self, state: PlaybackState):
        """更新播放状态

        参数:
            state: 新的播放状态
        """
        if self.playback_state == state:
            return

        self.playback_state = state

        # 调用回调函数
        if self.on_state_change:
            self.on_state_change(state)

    async def _stats_updater(self):
        """统计信息更新任务"""
        while self.is_running:
            try:
                # 更新统计信息
                if self.on_stats_update:
                    self.on_stats_update(self.stats.to_dict())

                # 等待下一次更新
                await asyncio.sleep(self.stats_update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"统计信息更新错误: {e}")
                await asyncio.sleep(1.0)

    async def _memory_monitor(self):
        """内存监控任务"""
        while self.is_running:
            try:
                # 更新内存统计
                cache_size_bytes = self._audio_cache.get_cache_size_bytes()
                cache_count = self._audio_cache.get_cache_count()
                self.stats.memory_stats.update_memory_usage(cache_size_bytes, cache_count)

                # 更新队列统计
                tts_stats = self.tts_queue.get_stats()
                audio_stats = self.audio_queue.get_stats()

                self.stats.queue_stats.tts_queue_size = tts_stats["current_size"]
                self.stats.queue_stats.audio_queue_size = audio_stats["current_size"]
                self.stats.queue_stats.tts_queue_max_size = tts_stats["max_size"]
                self.stats.queue_stats.audio_queue_max_size = audio_stats["max_size"]
                self.stats.queue_stats.tts_queue_full_count = tts_stats["full_count"]
                self.stats.queue_stats.audio_queue_full_count = audio_stats["full_count"]
                self.stats.queue_stats.total_enqueued = tts_stats["total_enqueued"] + audio_stats["total_enqueued"]
                self.stats.queue_stats.total_dequeued = tts_stats["total_dequeued"] + audio_stats["total_dequeued"]

                # 检查内存压力
                memory_mb = self.stats.memory_stats.cache_memory_mb
                if memory_mb > self.cache_memory_mb * 0.9:  # 90%阈值
                    logger.warning(f"缓存内存使用率过高: {memory_mb:.1f}MB / {self.cache_memory_mb}MB")

                # 检查队列压力
                if (self.stats.queue_stats.tts_queue_size > self.stats.queue_stats.tts_queue_max_size * 0.8 or
                    self.stats.queue_stats.audio_queue_size > self.stats.queue_stats.audio_queue_max_size * 0.8):
                    logger.warning("队列使用率过高，可能存在处理瓶颈")

                # 等待下一次检查
                await asyncio.sleep(2.0)  # 每2秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"内存监控错误: {e}")
                await asyncio.sleep(1.0)

    def _filter_non_speech_content(self, text: str) -> str:
        """过滤掉不需要朗读的内容，只保留中英文字符和基本标点"""
        # 移除代码块
        text = re.sub(r'```[\s\S]*?```', '', text)
        # 移除Markdown链接，只保留链接文本
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
        # 移除HTML标签
        text = re.sub(r'<[^>]*>', '', text)
        # 只保留中英文字符、数字和基本标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9。，？！、""''（）(),.?! \s]', '', text)
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        # 替换连续的标点符号为单个
        text = re.sub(r'[,.。，]{2,}', '，', text)
        text = re.sub(r'[!！]{2,}', '！', text)
        text = re.sub(r'[?？]{2,}', '？', text)
        return text.strip()

    async def _process_worker(self):
        """文本处理工作循环，将文本转换为音频数据"""
        while self.is_running:
            try:
                # 从队列获取文本
                text = await self.tts_queue.get()
                if not text:
                    self.tts_queue.task_done()
                    continue

                # 生成音频数据
                audio_data = await self._generate_audio(text)
                if audio_data:
                    # 将音频数据放入队列
                    await self.audio_queue.put(audio_data)

                self.tts_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"音频处理错误: {e}")
                self.tts_queue.task_done()
                await asyncio.sleep(0.1)

    def _get_cache_key(self, text: str) -> str:
        """生成缓存键

        参数:
            text: 文本内容

        返回:
            缓存键
        """
        # 使用文本、语音、语速和音量生成缓存键
        key_data = f"{text}|{self.voice}|{self.rate}|{self.volume}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _add_to_cache(self, text: str, audio_data: io.BytesIO):
        """添加到缓存

        参数:
            text: 文本内容
            audio_data: 音频数据
        """
        if not self.enable_cache:
            return

        # 生成缓存键
        cache_key = self._get_cache_key(text)

        # 使用智能缓存管理器
        success = self._audio_cache.put(cache_key, audio_data)
        if not success:
            logger.debug(f"缓存添加失败: {text[:20]}...")

    def _get_from_cache(self, text: str) -> Optional[io.BytesIO]:
        """从缓存获取

        参数:
            text: 文本内容

        返回:
            缓存的音频数据，如果没有找到则返回None
        """
        if not self.enable_cache:
            return None

        # 生成缓存键
        cache_key = self._get_cache_key(text)

        # 从智能缓存管理器获取
        audio_data = self._audio_cache.get(cache_key)

        if audio_data:
            # 更新统计信息
            self.stats.cache_hits += 1
            return audio_data
        else:
            # 更新统计信息
            self.stats.cache_misses += 1
            return None

    async def _generate_audio(self, text: str) -> Optional[io.BytesIO]:
        """生成音频数据

        参数:
            text: 要转换为语音的文本

        返回:
            音频数据，如果生成失败则返回None
        """
        # 更新统计信息
        self.stats.total_chunks += 1
        self.stats.total_characters += len(text)

        # 尝试从缓存获取
        cached_audio = self._get_from_cache(text)
        if cached_audio:
            logger.debug(f"从缓存获取音频: '{text[:20]}{'...' if len(text) > 20 else ''}'")
            return cached_audio

        # 记录开始时间
        start_time = time.time()

        # 重试机制
        for attempt in range(self.retry_attempts):
            try:
                # 创建通信对象
                communicate = edge_tts.Communicate(text, self.voice, rate=self.rate, volume=self.volume)

                # 使用流式处理方法
                audio_data = io.BytesIO()
                received_audio = False

                # 从流中收集音频数据
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data.write(chunk["data"])
                        received_audio = True

                # 检查是否收到了音频数据
                if not received_audio:
                    logger.warning(f"警告: 文本 '{text[:20]}{'...' if len(text) > 20 else ''}' 没有生成音频数据")
                    if attempt < self.retry_attempts - 1:
                        logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                        await asyncio.sleep(self.retry_delay)
                        continue
                    return None

                # 重置指针到开始位置
                audio_data.seek(0)

                # 添加到缓存
                self._add_to_cache(text, audio_data)

                # 更新统计信息
                self.stats.processing_time += time.time() - start_time

                return audio_data
            except Exception as e:
                logger.error(f"生成音频错误: {e}")
                if attempt < self.retry_attempts - 1:
                    logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    return None

    async def _audio_player(self):
        """音频播放循环，从队列获取音频数据并播放"""
        first_audio_played = False
        audio_block_count = 0  # 添加计数器跟踪已播放的音频块数量

        while self.is_running:
            try:
                # 获取音频数据
                audio_data = await self.audio_queue.get()
                if not audio_data:
                    self.audio_queue.task_done()
                    continue

                # 播放音频
                self.current_playing = audio_data

                # 记录播放开始时间
                play_start_time = time.time()

                # 播放音频
                pygame.mixer.music.load(audio_data)
                pygame.mixer.music.play()

                # 增加计数器
                audio_block_count += 1

                # 更新状态
                self._update_playback_state(PlaybackState.PLAYING)

                # 如果是第一个音频块，记录时间
                if not first_audio_played:
                    first_audio_played = True
                    self.stats.first_audio_time = time.time()

                    # 格式化时间戳为时:分:秒.毫秒
                    audio_time = self.stats.first_audio_time
                    timestamp = time.strftime("%H:%M:%S", time.localtime(audio_time))
                    ms = int((audio_time - int(audio_time)) * 1000)
                    formatted_time = f"{timestamp}.{ms:03d}"

                    # 记录开始播放第一个音频的时间
                    logger.info(f"TTS开始朗读: {formatted_time}")

                    # 如果已经记录了第一个token的时间，计算延迟
                    if self.stats.first_token_time > 0:
                        self.stats.time_to_first_audio = self.stats.first_audio_time - self.stats.first_token_time

                # 等待播放完成
                logger.debug(f"音频块 #{audio_block_count}: 等待播放完成")
                while pygame.mixer.music.get_busy() and self.is_running:
                    await asyncio.sleep(0.005)

                # 如果播放正常完成（而不是被停止），更新统计信息
                if self.is_running and self.playback_state == PlaybackState.PLAYING:
                    # 估计音频时长
                    audio_duration = time.time() - play_start_time
                    self.stats.total_audio_time += audio_duration

                self.current_playing = None
                self.audio_queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"音频播放错误: {e}")
                self.audio_queue.task_done()
                await asyncio.sleep(0.1)

    async def process_stream(self, stream: AsyncGenerator[Any, None], content_extractor: Callable[[Any], Optional[str]]):
        """处理流式输出

        参数:
            stream: 流式数据源
            content_extractor: 从流中提取文本内容的函数
        """
        # 重置状态
        self.first_chunk = True
        self.buffer = ""

        # 记录开始时间
        process_start_time = time.time()
        first_token_received = False

        try:
            # 更新状态
            self._update_playback_state(PlaybackState.PLAYING)

            # 处理流
            chunk_count = 0
            async for chunk in stream:
                # 检查是否已停止
                if not self.is_running:
                    logger.info("检测到停止信号，中断流处理")
                    break

                # 从chunk中提取文本
                delta_content = content_extractor(chunk)
                if delta_content is None:
                    continue

                # 检查是否是第一个token
                if not first_token_received and delta_content.strip():
                    first_token_received = True
                    self.stats.first_token_time = time.time()

                # 累积文本到缓冲区
                self.buffer += delta_content
                chunk_count += 1

                # 立即处理缓冲区
                await self._process_buffer()

                # 每处理10个块记录一次日志
                if chunk_count % 10 == 0:
                    logger.debug(f"已处理 {chunk_count} 个文本块")

            # 只有在正常运行状态下才处理剩余文本
            if self.is_running and self.buffer:
                filtered_text = self._filter_non_speech_content(self.buffer)
                if filtered_text.strip():
                    await self.tts_queue.put(filtered_text)
                    if self.on_text_chunk:
                        self.on_text_chunk(filtered_text)
                self.buffer = ""

            # 只有在正常运行状态下才等待任务完成
            if self.is_running:
                # 等待所有任务完成
                logger.info("等待所有TTS任务完成...")
                await self.tts_queue.join()

                logger.info("等待所有音频播放任务完成...")
                await self.audio_queue.join()

                # 记录总处理时间
                total_process_time = time.time() - process_start_time
                logger.info(f"流处理完成，总耗时: {total_process_time:.2f}秒")

                # 记录统计信息
                stats = self.stats.to_dict()
                logger.info(f"处理统计: 文本块={stats['total_chunks']}, 字符数={stats['total_characters']}, "
                           f"音频时长={stats['total_audio_time']:.2f}秒, 缓存命中率={stats['cache_hit_rate']*100:.1f}%")
            else:
                logger.info("流处理被停止，跳过等待任务完成")

        except asyncio.CancelledError:
            logger.info("流处理被取消")
            raise
        except Exception as e:
            logger.error(f"流处理错误: {e}")
            raise

    async def _warmup_tts(self, text: str):
        """预热TTS引擎，减少首次延迟"""
        try:
            # 创建通信对象
            communicate = edge_tts.Communicate(text, self.voice, rate=self.rate, volume=self.volume)

            # 使用流式处理方法但不保存结果
            async for _ in communicate.stream():
                pass
        except Exception as e:
            logger.debug(f"TTS预热过程中出现错误: {e}")

    def _segment_chinese_text(self, text: str, max_words: int) -> int:
        """使用分词处理，限制最大词数

        参数:
            text: 要处理的文本
            max_words: 最大词数

        返回:
            断句位置（字符索引）
        """
        if not text:
            return 0

        # 使用jieba分词
        words = list(jieba.cut(text))

        # 在控制台输出完整的分词结果
        print(f"完整分词结果: {' | '.join(words)}")

        # 限制最大词数
        if len(words) <= max_words:
            print(f"分词数量({len(words)})不超过最大词数({max_words})，使用全部文本")
            return len(text)

        # 计算前max_words个词的总长度
        selected_words = words[:max_words]
        total_length = sum(len(word) for word in selected_words)
        print(f"分词数量({len(words)})超过最大词数({max_words})，截取前{max_words}个词: {' | '.join(selected_words)}")
        return total_length

    def _find_break_positions(self, break_chars: List[str]) -> List[int]:
        """查找指定断句字符在缓冲区中的位置

        参数:
            break_chars: 断句字符列表

        返回:
            断句位置列表（包含断句字符）
        """
        positions = []
        for char in break_chars:
            pos = self.buffer.find(char)
            if pos != -1:
                positions.append(pos + len(char))  # 包含断句字符
        return positions

    async def _process_text_chunk(self, text: str, break_type: BreakType):
        """处理文本块

        参数:
            text: 要处理的文本
            break_type: 断句类型
        """
        # 检查是否已停止
        if not self.is_running:
            return

        filtered_text = self._filter_non_speech_content(text)
        if not filtered_text.strip():
            return

        # 记录日志
        log_prefix = {
            BreakType.FIRST_CHUNK: "首块",
            BreakType.SENTENCE: "句子",
            BreakType.CLAUSE: "从句",
            BreakType.FORCED: "强制"
        }.get(break_type, "未知")

        logger.debug(f"处理{log_prefix}文本块: {filtered_text[:20]}{'...' if len(filtered_text) > 20 else ''}")

        # 发送到TTS队列（使用自适应队列）
        await self.tts_queue.put(filtered_text)

        # 调用回调函数
        if self.on_text_chunk:
            self.on_text_chunk(filtered_text)

    async def _process_buffer(self):
        """处理缓冲区中的文本，根据不同情况进行断句"""
        # 检查是否已停止
        if not self.is_running:
            return

        # 如果是第一个文本块，根据配置选择断句方式
        if self.first_chunk and len(self.buffer) >= self.first_chunk_size:
            split_pos = 0

            if self.first_chunk_break_mode == FirstChunkBreakMode.PUNCTUATION:
                # 使用标点符号方式断句，包括所有标点符号
                all_punctuations = self._sentence_ends + self._clause_breaks + self._other_breaks
                punct_positions = self._find_break_positions(all_punctuations)

                if punct_positions:
                    # 找到最近的标点符号
                    split_pos = min(punct_positions)
                    logger.debug(f"第一个文本块使用标点符号断句，断句位置: {split_pos}")

                    # 处理文本块
                    sentence = self.buffer[:split_pos]
                    self.buffer = self.buffer[split_pos:]
                    await self._process_text_chunk(sentence, BreakType.FIRST_CHUNK)
                    self.first_chunk = False
                # 如果没有找到标点符号，保持在缓冲区中等待更多文本
                return
            else:  # FirstChunkBreakMode.WORD_SEGMENTATION
                # 使用分词方式断句
                split_pos = self._segment_chinese_text(self.buffer, self.first_chunk_size)
                logger.debug(f"第一个文本块使用分词方式，断句位置: {split_pos}")

                # 确保分割点不超过缓冲区长度
                split_pos = min(split_pos, len(self.buffer))
                # 确保至少有一个字符
                split_pos = max(split_pos, 1)

                sentence = self.buffer[:split_pos]
                self.buffer = self.buffer[split_pos:]

                # 记录处理结果
                words = list(jieba.cut(sentence))
                print(f"首块分词结果: {' | '.join(words)}")
                logger.debug(f"第一个文本块使用分词处理，最大词数: {self.first_chunk_size}，实际词数: {len(words)}，内容: {sentence}")

                await self._process_text_chunk(sentence, BreakType.FIRST_CHUNK)
                self.first_chunk = False
            return

        # 对于后续文本块，优先按照完整句子断句
        # 1. 首先尝试在句子结束标记处断句
        sentence_end_positions = self._find_break_positions(self._sentence_ends)
        if sentence_end_positions and len(self.buffer) >= self.min_sentence_size:
            # 找到最近的句子结束标记
            split_pos = min(sentence_end_positions)
            sentence = self.buffer[:split_pos]
            self.buffer = self.buffer[split_pos:]

            await self._process_text_chunk(sentence, BreakType.SENTENCE)
            return

        # 2. 如果没有找到句子结束标记，尝试在从句断句点处断句
        clause_positions = self._find_break_positions(self._clause_breaks)
        if clause_positions and len(self.buffer) >= self.min_sentence_size:
            # 找到最近的从句断句点
            split_pos = min(clause_positions)
            sentence = self.buffer[:split_pos]
            self.buffer = self.buffer[split_pos:]

            await self._process_text_chunk(sentence, BreakType.CLAUSE)
            return

        # 3. 如果缓冲区已经非常长，但仍然没有找到任何断句点，强制断句
        if len(self.buffer) >= self.max_chunk_size:
            # 尝试在其他可能的分割点处断句
            potential_breaks = []
            for br in self._other_breaks:
                pos = self.buffer.rfind(br, 0, self.max_chunk_size)
                if pos > 0:
                    potential_breaks.append(pos + 1)  # 包含分割点

            if potential_breaks:
                # 使用找到的最靠后的分割点
                split_pos = max(potential_breaks)
            else:
                # 如果没有找到合适的分割点，使用最大块大小的一半
                split_pos = self.max_chunk_size // 2

            # 确保不超过缓冲区长度
            split_pos = min(split_pos, len(self.buffer))

            sentence = self.buffer[:split_pos]
            self.buffer = self.buffer[split_pos:]

            await self._process_text_chunk(sentence, BreakType.FORCED)
            return
