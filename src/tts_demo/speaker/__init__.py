"""
TTS语音合成模块

提供多种TTS引擎的支持：
- Edge TTS (推荐)
- Google TTS (gTTS)
- pyttsx3
- 流式语音合成
"""

from .stream_speaker import StreamSpeaker, PlaybackState

# 导入其他speaker模块的函数（如果需要的话）
try:
    from .edge_speaker import speak as edge_speak, speak_async as edge_speak_async
except ImportError:
    edge_speak = None
    edge_speak_async = None

try:
    from .gtts_speaker import GTTSSpeaker
except ImportError:
    GTTSSpeaker = None

try:
    from .tts_speaker import TTSSpeaker
except ImportError:
    TTSSpeaker = None

__all__ = [
    "StreamSpeaker",
    "PlaybackState",
]

# 只导出存在的类
if GTTSSpeaker is not None:
    __all__.append("GTTSSpeaker")
if TTSSpeaker is not None:
    __all__.append("TTSSpeaker")
if edge_speak is not None:
    __all__.extend(["edge_speak", "edge_speak_async"])
