"""
核心数据模型

定义TTS系统中使用的核心数据结构。
"""

import io
import time
from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, Dict, Any


class PlaybackState(Enum):
    """播放状态枚举"""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"
    ERROR = "error"


class BreakType(Enum):
    """断句类型枚举"""
    SENTENCE = "sentence"      # 完整句子
    CLAUSE = "clause"          # 从句
    FORCED = "forced"          # 强制断句
    FIRST_CHUNK = "first_chunk"  # 第一个文本块


@dataclass
class TextChunk:
    """文本块数据模型"""
    content: str
    break_type: BreakType
    position: int = 0
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __len__(self) -> int:
        return len(self.content)

    def is_empty(self) -> bool:
        return not self.content.strip()


@dataclass
class AudioData:
    """音频数据模型"""
    data: io.BytesIO
    format: str = "mp3"
    sample_rate: int = 24000
    duration: float = 0.0
    text: str = ""
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def get_size_bytes(self) -> int:
        """获取音频数据大小（字节）"""
        current_pos = self.data.tell()
        self.data.seek(0, 2)  # 移动到末尾
        size = self.data.tell()
        self.data.seek(current_pos)  # 恢复原位置
        return size

    def copy(self) -> 'AudioData':
        """创建音频数据副本"""
        self.data.seek(0)
        copied_data = io.BytesIO(self.data.getvalue())
        self.data.seek(0)

        return AudioData(
            data=copied_data,
            format=self.format,
            sample_rate=self.sample_rate,
            duration=self.duration,
            text=self.text,
            timestamp=self.timestamp,
            metadata=self.metadata.copy()
        )


@dataclass
class MemoryStats:
    """内存使用统计"""
    cache_memory_mb: float = 0.0
    process_memory_mb: float = 0.0
    system_memory_percent: float = 0.0
    cache_items_count: int = 0
    max_cache_memory_mb: float = 100.0


@dataclass
class QueueStats:
    """队列统计信息"""
    current_size: int = 0
    max_size: int = 10
    full_count: int = 0
    total_enqueued: int = 0
    total_dequeued: int = 0


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_chunks: int = 0
    total_characters: int = 0
    total_audio_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    start_time: float = field(default_factory=time.time)
    processing_time: float = 0.0
    first_token_time: float = 0.0
    first_audio_time: float = 0.0
    time_to_first_audio: float = 0.0
    memory_stats: MemoryStats = field(default_factory=MemoryStats)
    queue_stats: QueueStats = field(default_factory=QueueStats)

    @property
    def elapsed_time(self) -> float:
        """计算已用时间"""
        return time.time() - self.start_time

    @property
    def chars_per_second(self) -> float:
        """计算每秒处理字符数"""
        if self.elapsed_time > 0:
            return self.total_characters / self.elapsed_time
        return 0.0

    @property
    def cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        total_requests = self.cache_hits + self.cache_misses
        if total_requests > 0:
            return self.cache_hits / total_requests
        return 0.0

    def reset(self):
        """重置统计信息"""
        self.total_chunks = 0
        self.total_characters = 0
        self.total_audio_time = 0.0
        self.cache_hits = 0
        self.cache_misses = 0
        self.start_time = time.time()
        self.processing_time = 0.0
        self.first_token_time = 0.0
        self.first_audio_time = 0.0
        self.time_to_first_audio = 0.0
        self.memory_stats = MemoryStats()
        self.queue_stats = QueueStats()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_chunks": self.total_chunks,
            "total_characters": self.total_characters,
            "total_audio_time": self.total_audio_time,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "elapsed_time": self.elapsed_time,
            "chars_per_second": self.chars_per_second,
            "cache_hit_rate": self.cache_hit_rate,
            "processing_time": self.processing_time,
            "first_token_time": self.first_token_time,
            "first_audio_time": self.first_audio_time,
            "time_to_first_audio": self.time_to_first_audio,
            "memory_stats": {
                "cache_memory_mb": self.memory_stats.cache_memory_mb,
                "process_memory_mb": self.memory_stats.process_memory_mb,
                "system_memory_percent": self.memory_stats.system_memory_percent,
                "cache_items_count": self.memory_stats.cache_items_count,
                "max_cache_memory_mb": self.memory_stats.max_cache_memory_mb
            },
            "queue_stats": {
                "current_size": self.queue_stats.current_size,
                "max_size": self.queue_stats.max_size,
                "full_count": self.queue_stats.full_count,
                "total_enqueued": self.queue_stats.total_enqueued,
                "total_dequeued": self.queue_stats.total_dequeued
            }
        }


@dataclass
class TTSConfig:
    """TTS配置数据模型"""
    voice: str = "zh-CN-YunjianNeural"
    rate: str = "+0%"
    volume: str = "+0%"
    first_chunk_size: int = 5
    min_sentence_size: int = 20
    max_chunk_size: int = 500
    queue_size: int = 10
    max_queue_size: int = 50
    enable_cache: bool = True
    cache_size: int = 100
    cache_memory_mb: float = 100.0
    debug_mode: bool = False
    audio_frequency: int = 24000
    stats_update_interval: float = 5.0
    retry_attempts: int = 3
    retry_delay: float = 0.5


