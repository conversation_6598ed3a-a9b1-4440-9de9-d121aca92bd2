"""
TTS Demo 核心模块

提供核心接口、抽象类和基础组件：
- 接口定义
- 抽象基类
- 事件系统
- 异常定义
"""

from .interfaces import (
    TextProcessor,
    AudioGenerator,
    AudioPlayer,
    CacheManager,
    QueueManager,
    EventBus,
    TTSEngine
)

from .exceptions import (
    TTSError,
    TextProcessingError,
    AudioGenerationError,
    AudioPlaybackError,
    CacheError,
    QueueError
)

from .events import (
    Event,
    TextChunkEvent,
    AudioGeneratedEvent,
    PlaybackStateEvent,
    StatsUpdateEvent,
    ErrorEvent
)

from .models import (
    TextChunk,
    AudioData,
    PlaybackState,
    ProcessingStats
)

__all__ = [
    # 接口
    "TextProcessor",
    "AudioGenerator", 
    "AudioPlayer",
    "CacheManager",
    "QueueManager",
    "EventBus",
    "TTSEngine",
    
    # 异常
    "TTSError",
    "TextProcessingError",
    "AudioGenerationError",
    "AudioPlaybackError",
    "CacheError",
    "QueueError",
    
    # 事件
    "Event",
    "TextChunkEvent",
    "AudioGeneratedEvent",
    "PlaybackStateEvent",
    "StatsUpdateEvent",
    "ErrorEvent",
    
    # 模型
    "TextChunk",
    "AudioData",
    "PlaybackState",
    "ProcessingStats"
]
