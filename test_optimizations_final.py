#!/usr/bin/env python3
"""
测试内存管理和队列优化功能
"""

import asyncio
import io
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from tts_demo.speaker.stream_speaker import SmartAudioCache, AdaptiveQueue, MemoryStats, QueueStats


async def test_smart_cache():
    """测试智能音频缓存"""
    print("🧪 测试智能音频缓存...")
    
    # 创建缓存，限制为3个项目，0.1MB内存
    cache = SmartAudioCache(max_items=3, max_memory_mb=0.1)
    
    print(f"初始状态: 项数={cache.get_cache_count()}, 内存={cache.get_cache_size_bytes()}B")
    
    # 添加测试数据
    for i in range(5):
        # 创建约16KB的测试音频数据
        test_data = io.BytesIO(b'test audio data ' * 1000)
        key = f'audio_key_{i}'
        
        success = cache.put(key, test_data)
        print(f"  添加项目 {i}: {'成功' if success else '失败'} - "
              f"项数={cache.get_cache_count()}, "
              f"内存={cache.get_cache_size_bytes()/1024:.1f}KB")
    
    # 测试缓存获取
    print("  测试缓存获取:")
    for i in range(5):
        key = f'audio_key_{i}'
        retrieved = cache.get(key)
        print(f"    获取项目 {i}: {'成功' if retrieved else '失败'}")
    
    return True


async def test_adaptive_queue():
    """测试自适应队列"""
    print("\n🔄 测试自适应队列...")
    
    # 创建队列，初始大小2，最大8
    queue = AdaptiveQueue(initial_size=2, max_size=8)
    
    print(f"初始队列大小: {queue.current_max_size}")
    
    # 添加项目测试扩容
    for i in range(6):
        await queue.put(f'item_{i}')
        stats = queue.get_stats()
        print(f"  添加项目 {i}: "
              f"当前大小={stats['current_size']}/{stats['max_size']}, "
              f"满次数={stats['full_count']}")
    
    # 测试获取
    print("  测试队列获取:")
    for i in range(3):
        item = await queue.get()
        queue.task_done()
        stats = queue.get_stats()
        print(f"    获取项目: {item}, 当前大小={stats['current_size']}")
    
    return True


def test_memory_stats():
    """测试内存统计"""
    print("\n📊 测试内存统计...")
    
    stats = MemoryStats()
    
    # 模拟更新内存使用
    stats.update_memory_usage(50 * 1024 * 1024, 10)  # 50MB, 10个项目
    
    print(f"  缓存内存: {stats.cache_memory_mb:.1f}MB")
    print(f"  进程内存: {stats.process_memory_mb:.1f}MB")
    print(f"  系统内存使用率: {stats.system_memory_percent:.1f}%")
    print(f"  缓存项数: {stats.cache_items_count}")
    
    return True


def test_queue_stats():
    """测试队列统计"""
    print("\n📈 测试队列统计...")
    
    stats = QueueStats()
    stats.tts_queue_size = 5
    stats.audio_queue_size = 3
    stats.tts_queue_max_size = 10
    stats.audio_queue_max_size = 10
    stats.total_enqueued = 100
    stats.total_dequeued = 95
    
    print(f"  TTS队列: {stats.tts_queue_size}/{stats.tts_queue_max_size}")
    print(f"  音频队列: {stats.audio_queue_size}/{stats.audio_queue_max_size}")
    print(f"  总入队: {stats.total_enqueued}")
    print(f"  总出队: {stats.total_dequeued}")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始测试内存管理和队列优化...")
    
    try:
        success = True
        
        success &= await test_smart_cache()
        success &= await test_adaptive_queue()
        success &= test_memory_stats()
        success &= test_queue_stats()
        
        if success:
            print("\n✅ 所有优化功能测试通过!")
            print("\n📋 优化总结:")
            print("  ✅ 智能音频缓存: 支持内存限制和自动清理")
            print("  ✅ 自适应队列: 支持动态扩容和统计监控")
            print("  ✅ 内存监控: 实时监控内存和队列使用情况")
            print("  ✅ 统计系统: 详细的性能和资源使用统计")
        else:
            print("\n❌ 部分测试失败")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
