# TTS Demo - 大模型流式TTS演示程序
# 依赖管理工具: uv (https://docs.astral.sh/uv/)
# 安装依赖: uv sync

[project]
name = "tts_demo"
version = "0.2.0"
description = "大模型流式TTS演示程序 - 将大模型的流式文本输出实时转换为语音"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "TTS Demo Team"}
]
keywords = ["tts", "llm", "streaming", "speech", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Sound/Audio :: Speech",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "aiohttp>=3.11.18",
    "edge-tts>=7.0.2",
    "fastapi>=0.115.12",
    "gtts>=2.5.4",
    "jieba>=0.42.1",
    "jinja2>=3.1.6",
    "openai>=1.79.0",
    "pygame>=2.6.1",
    "pydantic>=2.0.0",
    "python-dotenv>=1.1.0",
    "python-multipart>=0.0.20",
    "pyttsx3>=2.98",
    "uvicorn>=0.34.2",
    "websockets>=15.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[project.scripts]
tts-demo = "tts_demo.main:main"
tts-web = "scripts.start_web:main"

[project.urls]
Homepage = "https://github.com/your-username/tts_demo"
Repository = "https://github.com/your-username/tts_demo.git"
Documentation = "https://github.com/your-username/tts_demo/blob/main/docs/README.md"
Issues = "https://github.com/your-username/tts_demo/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/tts_demo"]

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
